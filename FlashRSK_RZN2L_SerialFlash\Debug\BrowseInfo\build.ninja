##IAR Ninja build file


#Rules
rule COMPILER_XCL
  command = "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\common\bin\XclFileGenerator.exe" $xclcommand -f "$rspfile_name"
  description = IAR_NEW_TOOL+++COMPILER_XCL+++$out
  rspfile = $rspfile_name
  rspfile_content = $flags

rule INDEXER
  command = "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\common\bin\SourceIndexer.exe" $flags
  depfile = $out.dep
  deps = gcc
  description = IAR_NEW_TOOL+++INDEXER+++$out

rule MAKEBROWSE
  command = "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\common\bin\makeBrowseData.exe" $flags
  description = IAR_NEW_TOOL+++MAKEBROWSE+++$out

rule PDBLINK
  command = "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\common\bin\PbdLink.exe" $flags
  description = IAR_NEW_TOOL+++PDBLINK+++$out



#Build steps
build D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.xcl : COMPILER_XCL 
    flags = "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\src\flashloader\framework2\flash_loader.c" -o D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\Obj\framework_4913715117250599538.dir --debug --endian=little --cpu=Cortex-R52 -e --fpu=VFPv5 --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\src\flashloader\framework2\\" -I D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\ --cpu_mode thumb -Ohz --predef_macros D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.tmp
    rspfile_name = D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.xcl.rsp
    xclcommand = -source_file "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\src\flashloader\framework2\flash_loader.c" -xcl_file D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.xcl -macro_file D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.xcl : COMPILER_XCL 
    flags = D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\MX25UR51245G.c -o D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\Obj\ --debug --endian=little --cpu=Cortex-R52 -e --fpu=VFPv5 --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\src\flashloader\framework2\\" -I D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\ --cpu_mode thumb -Ohz --predef_macros D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.tmp
    rspfile_name = D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.xcl.rsp
    xclcommand = -source_file D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\MX25UR51245G.c -xcl_file D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.xcl -macro_file D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.xcl : COMPILER_XCL 
    flags = D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\MX25UR51245G_flash_loader.c -o D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\Obj\ --debug --endian=little --cpu=Cortex-R52 -e --fpu=VFPv5 --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\src\flashloader\framework2\\" -I D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\ --cpu_mode thumb -Ohz --predef_macros D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.tmp
    rspfile_name = D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.xcl.rsp
    xclcommand = -source_file D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\MX25UR51245G_flash_loader.c -xcl_file D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.xcl -macro_file D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.pbi : INDEXER D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.xcl | C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\src\flashloader\framework2\flash_loader.c
    flags = -out=D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.pbi -f D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.xcl

build D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.pbi : INDEXER D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.xcl | D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\MX25UR51245G.c
    flags = -out=D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.pbi -f D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.xcl

build D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.pbi : INDEXER D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.xcl | D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\MX25UR51245G_flash_loader.c
    flags = -out=D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.pbi -f D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.xcl

build D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\FlashRSK_RZN2L_SerialFlash.pbw : MAKEBROWSE D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\FlashRSK_RZN2L_SerialFlash.pbd
    flags = D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\FlashRSK_RZN2L_SerialFlash.pbd -output D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\FlashRSK_RZN2L_SerialFlash.pbw

build D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\FlashRSK_RZN2L_SerialFlash.pbd : PDBLINK D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.pbi | D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.pbi $
 D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.pbi
    flags = -M D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\FlashRSK_RZN2L_SerialFlash.pbd D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\framework_4913715117250599538.dir\flash_loader.pbi D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G.pbi D$:\Code\Ruisa\rzn2l-1Axis-lv\1_LV_Axis_flash_App_gitee\FlashRSK_RZN2L_SerialFlash\Debug\BrowseInfo\MX25UR51245G_flash_loader.pbi

