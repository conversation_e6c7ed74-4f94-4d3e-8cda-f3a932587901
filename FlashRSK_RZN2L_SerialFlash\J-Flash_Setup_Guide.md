# J-Flash Setup Guide for RZ/N2L with MX25UR51245G SPI Flash

## Overview

This guide explains how to use the J-Link Script and J-Flash project files to program the MX25UR51245G SPI Flash on the Renesas RZ/N2L (Cortex-R52) development board.

## Files Description

### 1. RZN2L_MX25UR51245G_JLink.JLinkScript
This is the main J-Link script file that contains:
- Target initialization functions
- System clock configuration (400MHz)
- XSPI pin setup for SPI flash interface
- BTCM/ATCM memory configuration
- Hardware reset and setup procedures

### 2. RZN2L_MX25UR51245G.jflash
This is the J-Flash project configuration file that defines:
- Flash device parameters (MX25UR51245G)
- Memory mapping (Flash base address: 0x68000000)
- Sector and block sizes
- Programming options

## Hardware Configuration

### Target Device
- **MCU**: Renesas RZ/N2L (Cortex-R52)
- **Flash**: MX25UR51245G (64MB SPI Flash)
- **Interface**: SWD
- **Flash Base Address**: 0x68000000

### Pin Configuration
The script configures the following XSPI1 pins:
- P17_7 → XSPI1_CKP (Clock)
- P16_7 → XSPI1_IO0 (Data 0)
- P17_0 → XSPI1_IO1 (Data 1)
- P17_3 → XSPI1_IO2 (Data 2)
- P17_4 → XSPI1_IO3 (Data 3)
- P18_2 → XSPI1_CS0# (Chip Select)

## Setup Instructions

### 1. J-Link Software Requirements
- J-Link Software v7.00 or later
- J-Flash application

### 2. Hardware Setup
1. Connect J-Link debugger to the RZ/N2L board via SWD interface
2. Ensure proper power supply to the target board
3. Verify that the MX25UR51245G SPI flash is properly connected

### 3. J-Flash Configuration

#### Method 1: Using the Project File
1. Open J-Flash
2. File → Open Project
3. Select `RZN2L_MX25UR51245G.jflash`
4. The project will load with all necessary settings

#### Method 2: Manual Configuration
1. Open J-Flash
2. Options → Project Settings
3. Configure the following:
   - **Device**: Select "Unspecified" or create custom device
   - **Interface**: SWD
   - **Speed**: 4000 kHz
   - **Target Voltage**: 3.3V
   - **Init File**: Browse and select `RZN2L_MX25UR51245G_JLink.JLinkScript`

4. Flash Configuration:
   - **Bank Name**: SPI Flash MX25UR51245G
   - **Bank Address**: 0x68000000
   - **Bank Size**: 0x4000000 (64MB)
   - **Sector Size**: 0x1000 (4KB)

### 4. Programming Operations

#### Connect to Target
1. Target → Connect
2. The script will automatically:
   - Reset the target
   - Configure system clock to 400MHz
   - Setup XSPI pins
   - Initialize BTCM/ATCM memory

#### Erase Flash
1. Target → Manual Programming → Erase Chip
2. Or select specific sectors: Target → Manual Programming → Erase Sectors

#### Program Flash
1. File → Open Data File
2. Select your binary/hex file
3. Target → Manual Programming → Program & Verify
4. Or use: Target → Production Programming

#### Verify Flash
1. Target → Manual Programming → Verify

## Flash Device Specifications

### MX25UR51245G Parameters
- **Manufacturer**: Macronix
- **Device ID**: 0x0001840C8
- **Total Size**: 64MB (0x4000000 bytes)
- **Page Size**: 256 bytes (0x100)
- **Sector Size**: 4KB (0x1000)
- **Block Size**: 64KB (0x10000)
- **Address Mode**: 4-byte addressing
- **Interface**: SPI (1S-1S-1S mode)

### Memory Map
```
Flash Memory Layout:
0x68000000 - 0x6BFFFFFF: SPI Flash (64MB)
  - Sector 0: 0x68000000 - 0x68000FFF (4KB)
  - Sector 1: 0x68001000 - 0x68001FFF (4KB)
  - ...
  - Sector 16383: 0x6BFFF000 - 0x6BFFFFFF (4KB)
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check J-Link connection and power
   - Verify SWD interface settings
   - Ensure target voltage is 3.3V

2. **Flash ID Mismatch**
   - Verify the correct flash device is mounted
   - Check XSPI pin connections
   - Ensure proper power supply to flash

3. **Programming Errors**
   - Check flash is not write-protected
   - Verify sector alignment
   - Ensure sufficient target RAM for flash loader

4. **Slow Programming Speed**
   - Increase SWD speed if stable
   - Check target clock configuration
   - Verify XSPI clock settings

### Debug Information
The script provides detailed logging. Check the J-Flash log window for:
- Target initialization status
- Clock configuration results
- Pin setup confirmation
- Memory initialization status

## Advanced Configuration

### Custom Flash Loader
If you need to use a custom flash loader instead of the built-in J-Link flash programming:
1. Compile the IAR flashloader project
2. Use the generated `.out` file as external flash loader
3. Configure J-Flash to use external flash loader

### Performance Optimization
- Increase SWD speed for faster programming
- Use "Skip blank areas" option for faster programming
- Enable "Skip programming on CRC match" for incremental updates

## Support

For additional support:
- Refer to J-Link User Guide
- Check Renesas RZ/N2L documentation
- Contact SEGGER support for J-Link specific issues
- Contact Renesas support for target-specific issues
