# RZ/N2L J-Link 脚本问题快速修复指南

## 当前问题
您遇到的错误是J-Link脚本中使用了不存在的API函数：
- `JLINK_TARGET_Reset()` - 此函数不存在
- 可能还有其他不兼容的API

## 立即解决方案

### 🎯 **推荐测试顺序**

#### 1. 首先测试硬件连接（无脚本）
```batch
program_flash.bat
选择：1. Test connection (no script)
```
**目的**：确认硬件连接正常

#### 2. 测试J-Link脚本API兼容性
```batch
program_flash.bat
选择：2. Test J-Link Script APIs
```
**目的**：验证基本的脚本API是否工作

#### 3. 使用最安全的脚本连接
```batch
program_flash.bat
选择：3. Connect to target with script
```
**目的**：使用修复后的脚本连接

### 📁 **可用的脚本文件**

#### 🟢 **RZN2L_SafeAPI.JLinkScript** (当前推荐)
- 只使用确认存在的API
- 避免所有可能有问题的函数调用
- 不使用复位相关API

#### 🟡 **RZN2L_NoScript.jflash** (备用方案)
- 完全不使用脚本
- 用于硬件连接测试

#### 🔵 **RZN2L_APITest.JLinkScript** (诊断用)
- 测试哪些API可用
- 帮助诊断兼容性问题

### 🔧 **手动修复步骤**

如果自动脚本仍然失败，可以手动操作：

#### 步骤1：使用无脚本连接
1. 打开J-Flash
2. File → Open Project → `RZN2L_NoScript.jflash`
3. Target → Connect

#### 步骤2：手动初始化寄存器
在J-Flash的Memory窗口中手动写入：
```
地址: 0x81281A00, 值: 0x0000A50F  // 解锁PRCRS
地址: 0x80281A10, 值: 0x0000A50F  // 解锁PRCRN  
地址: 0x81280008, 值: 0x00000015  // 设置时钟400MHz
地址: 0x80280300, 值: 0x00001F01  // 使能xSPI模块
地址: 0x81281A00, 值: 0x0000A500  // 锁定PRCRS
地址: 0x80281A10, 值: 0x0000A500  // 锁定PRCRN
```

#### 步骤3：配置XSPI引脚
```
地址: 0x81030C10, 值: 0x80        // ASELP16
地址: 0x81030C11, 值: 0x99        // ASELP17
地址: 0x81030C12, 值: 0x04        // ASELP18
地址: 0x800A0640, 值: 0x20000000  // PFC16
地址: 0x800A0644, 值: 0x70077002  // PFC17
地址: 0x800A0648, 值: 0x00000500  // PFC18
地址: 0x800A0410, 值: 0x80        // PMC16
地址: 0x800A0411, 值: 0x99        // PMC17
地址: 0x800A0412, 值: 0x04        // PMC18
```

### 🚨 **常见错误及解决方案**

#### 错误1：`JLINK_TARGET_Reset() Syntax error`
**解决**：使用 `RZN2L_SafeAPI.JLinkScript`，已移除此函数

#### 错误2：`JLINK_MEM_Fill() Syntax error`
**解决**：使用 `RZN2L_SafeAPI.JLinkScript`，不使用此函数

#### 错误3：`Failed to connect`
**解决**：
1. 检查硬件连接
2. 使用无脚本配置测试
3. 降低SWD速度到1000 kHz

### 📊 **诊断流程图**

```
开始
  ↓
测试无脚本连接
  ↓
成功？ → 是 → 测试API脚本 → 成功？ → 是 → 使用SafeAPI脚本
  ↓                        ↓
  否                       否
  ↓                        ↓
检查硬件连接              手动初始化寄存器
```

### 🎯 **成功标志**

#### 连接成功的标志：
```
Connected successfully
Target halted
RZ/N2L Safe API Initialization
Safe API initialization completed
```

#### Flash操作成功的标志：
```
能够读取Flash ID
擦除操作成功
编程操作成功
```

### 📞 **如果仍然失败**

1. **检查J-Link版本**：确保使用V7.00或更高版本
2. **检查目标板**：确认电源、复位、时钟正常
3. **尝试其他工具**：
   - J-Link Commander直接操作
   - 原始IAR环境
   - Renesas Flash Programmer

### 🔄 **快速测试命令**

```batch
# 完整测试序列
test_connection.bat           # 基础连接测试
program_flash.bat → 1        # 无脚本连接测试  
program_flash.bat → 2        # API兼容性测试
program_flash.bat → 3        # 脚本连接测试
```

---

**最后更新**：2025-07-23  
**状态**：已修复JLINK_TARGET_Reset()错误  
**推荐脚本**：RZN2L_SafeAPI.JLinkScript
