/*********************************************************************
*                    SEGGER Microcontroller GmbH                     *
*                        The Embedded Experts                        *
**********************************************************************
*                                                                    *
* J-Link Script API Test for RZ/N2L                                  *
* This script tests which APIs are available                         *
*                                                                    *
**********************************************************************
*/

/*********************************************************************
*
*       InitTarget
*
*  Function description
*    Test basic APIs to see what works
*/
void InitTarget(void) {
  Report("=== J-Link Script API Test ===");
  
  // Test basic memory operations (these should always work)
  Report("Testing JLINK_MEM_WriteU32...");
  JLINK_MEM_WriteU32(0x81281A00, 0x0000A50F);
  Report("JLINK_MEM_WriteU32 - OK");
  
  Report("Testing JLINK_MEM_WriteU8...");
  JLINK_MEM_WriteU8(0x81030C10, 0x80);
  Report("JLINK_MEM_WriteU8 - OK");
  
  // Basic system setup
  Report("Performing basic system setup...");
  JLINK_MEM_WriteU32(0x81281A00, 0x0000A50F);  // Unlock PRCRS
  JLINK_MEM_WriteU32(0x80281A10, 0x0000A50F);  // Unlock PRCRN
  JLINK_MEM_WriteU32(0x81280008, 0x00000015);  // Set clock
  JLINK_MEM_WriteU32(0x80280300, 0x00001F01);  // Enable xSPI
  JLINK_MEM_WriteU32(0x81281A00, 0x0000A500);  // Lock PRCRS
  JLINK_MEM_WriteU32(0x80281A10, 0x0000A500);  // Lock PRCRN
  
  Report("=== API Test Completed Successfully ===");
}

/*********************************************************************
*
*       SetupTarget
*/
void SetupTarget(void) {
  Report("SetupTarget called");
}

/*********************************************************************
*
*       ResetTarget
*/
void ResetTarget(void) {
  Report("ResetTarget called - performing re-initialization");
  InitTarget();
}
