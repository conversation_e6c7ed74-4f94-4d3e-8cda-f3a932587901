/*********************************************************************
*                    SEGGER Microcontroller GmbH                     *
*                        The Embedded Experts                        *
**********************************************************************
*                                                                    *
* J-Link Script for Renesas RZ/N2L with MX25UR51245G SPI Flash      *
* Ultra-basic version with maximum compatibility                     *
*                                                                    *
**********************************************************************
*/

/*********************************************************************
*
*       InitTarget
*
*  Function description
*    Called before InitTarget() is called.
*    Mainly used to set some global DLL variables to customize the
*    normal connect procedure.
*/
void InitTarget(void) {
  Report("RZ/N2L Ultra-Basic Target Initialization");

  // Unlock protection registers
  JLINK_MEM_WriteU32(0x81281A00, 0x0000A50F);  // PRCRS unlock
  JLINK_MEM_WriteU32(0x80281A10, 0x0000A50F);  // PRCRN unlock

  // Set system clock to 400MHz
  JLINK_MEM_WriteU32(0x81280008, 0x00000015);  // SCKCR2

  // Enable xSPI module
  JLINK_MEM_WriteU32(0x80280300, 0x00001F01);  // MSTPCRA

  // Configure XSPI pins - simplified version
  JLINK_MEM_WriteU8(0x81030C10, 0x80);   // ASELP16
  JLINK_MEM_WriteU8(0x81030C11, 0x99);   // ASELP17
  JLINK_MEM_WriteU8(0x81030C12, 0x04);   // ASELP18

  JLINK_MEM_WriteU32(0x800A0640, 0x20000000);  // PFC16
  JLINK_MEM_WriteU32(0x800A0644, 0x70077002);  // PFC17
  JLINK_MEM_WriteU32(0x800A0648, 0x00000500);  // PFC18

  JLINK_MEM_WriteU8(0x800A0410, 0x80);   // PMC16
  JLINK_MEM_WriteU8(0x800A0411, 0x99);   // PMC17
  JLINK_MEM_WriteU8(0x800A0412, 0x04);   // PMC18

  // Lock protection registers
  JLINK_MEM_WriteU32(0x81281A00, 0x0000A500);  // PRCRS lock
  JLINK_MEM_WriteU32(0x80281A10, 0x0000A500);  // PRCRN lock

  Report("Target initialization completed");
}

/*********************************************************************
*
*       SetupTarget
*
*  Function description
*    Called after InitTarget() and after general J-Link setup.
*    Can be used to configure the target for flash programming.
*/
void SetupTarget(void) {
  Report("Setting up target for flash programming");
  // Additional setup if needed
}

/*********************************************************************
*
*       ResetTarget
*
*  Function description
*    Called to reset the target.
*/
void ResetTarget(void) {
  int i;  // Declare variable at function start

  Report("Resetting target");

  // Perform hardware reset using correct J-Link TIF API
  JLINK_TIF_ActivateTargetReset();   // Set nReset LOW

  // Reset pulse delay
  for (i = 0; i < 10000; i++) {
    // Reset pulse delay
  }

  JLINK_TIF_ReleaseTargetReset();    // Set nReset HIGH

  // Wait for target to stabilize
  for (i = 0; i < 50000; i++) {
    // Recovery delay
  }

  // Re-initialize after reset
  InitTarget();

  Report("Target reset completed");
}
