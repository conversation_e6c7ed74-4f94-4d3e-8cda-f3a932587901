/*********************************************************************
*                    SEGGER Microcontroller GmbH                     *
*                        The Embedded Experts                        *
**********************************************************************
*                                                                    *
*            (c) 2014 - 2024 SEGGER Microcontroller GmbH             *
*                                                                    *
*       www.segger.com     Support: <EMAIL>               *
*                                                                    *
**********************************************************************
*                                                                    *
* All rights reserved.                                               *
*                                                                    *
* Redistribution and use in source and binary forms, with or        *
* without modification, are permitted provided that the following   *
* conditions are met:                                                *
*                                                                    *
* - Redistributions of source code must retain the above copyright  *
*   notice, this list of conditions and the following disclaimer.   *
*                                                                    *
* - Neither the name of SEGGER Microcontroller GmbH                 *
*   nor the names of its contributors may be used to endorse or     *
*   promote products derived from this software without specific    *
*   prior written permission.                                       *
*                                                                    *
* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND            *
* CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,       *
* INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF          *
* MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE          *
* DISCLAIMED.                                                        *
* IN NO EVENT SHALL SEGGER Microcontroller GmbH BE LIABLE FOR       *
* ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR          *
* CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT *
* OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;   *
* OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF     *
* LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT         *
* (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE *
* USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH  *
* DAMAGE.                                                            *
*                                                                    *
**********************************************************************

-------------------------- END-OF-HEADER -----------------------------

File      : RZN2L_MX25UR51245G_JLink.JLinkScript
Purpose   : J-Link script file for Renesas RZ/N2L with MX25UR51245G SPI Flash
Literature:
  [1] J-Link User Guide

Additional information:
  For more information about public functions that can be implemented to customize J-Link actions, please refer to [1]
*/

/*********************************************************************
*
*       Constants (similar to #define)
*
**********************************************************************
*/

// Memory map definitions
__constant U32 INTVEC_START     = 0x10000000;
__constant U32 RAM_START        = 0x10001000;
__constant U32 RAM_END          = 0x1017FFFF;
__constant U32 FLASH_BASE       = 0x68000000;

// BTCM/ATCM definitions
__constant U32 BTCM_ADDR        = 0x00100000;
__constant U32 BTCM_SIZE        = 0x00020000;
__constant U32 ATCM_ADDR        = 0x00000000;
__constant U32 ATCM_SIZE        = 0x00020000;

// Register addresses for system control
__constant U32 PRCRS_ADDR       = 0x81281A00;  // Safety Area Protect Register
__constant U32 PRCRN_ADDR       = 0x80281A10;  // Non-Safety Area Protect Register
__constant U32 MSTPCRA_ADDR     = 0x80280300;  // Module Stop Control Register A
__constant U32 MSTPCRD_ADDR     = 0x80280330;  // Module Stop Control Register D
__constant U32 SCKCR_ADDR       = 0x81280004;  // System Clock Control Register
__constant U32 SCKCR2_ADDR      = 0x81280008;  // System Clock Control Register 2

// Protection register values
__constant U32 PRCRS_UNLOCK     = 0x0000A50F;
__constant U32 PRCRS_LOCK       = 0x0000A500;
__constant U32 PRCRN_UNLOCK     = 0x0000A50F;
__constant U32 PRCRN_LOCK       = 0x0000A500;

// Port control registers for XSPI pins
__constant U32 ASELP16_ADDR     = 0x81030C10;
__constant U32 ASELP17_ADDR     = 0x81030C11;
__constant U32 ASELP18_ADDR     = 0x81030C12;
__constant U32 PFC16_ADDR       = 0x800A0640;
__constant U32 PFC17_ADDR       = 0x800A0644;
__constant U32 PFC18_ADDR       = 0x800A0648;
__constant U32 PMC16_ADDR       = 0x800A0410;
__constant U32 PMC17_ADDR       = 0x800A0411;
__constant U32 PMC18_ADDR       = 0x800A0412;

// XSPI controller registers
__constant U32 XSPI1_BASE       = 0x80050000;
__constant U32 XSPI1_INTC       = 0x80050004;
__constant U32 XSPI1_COMSTT     = 0x80050008;
__constant U32 XSPI1_BMCTL0     = 0x80050020;
__constant U32 XSPI1_CDCTL0     = 0x80050040;
__constant U32 XSPI1_CDTBUF0    = 0x80050044;
__constant U32 XSPI1_CDABUF0    = 0x80050048;
__constant U32 XSPI1_CDD0BUF0   = 0x8005004C;
__constant U32 XSPI1_LIOCFGCS0  = 0x80050080;
__constant U32 XSPI1_INTS       = 0x80050100;

// CMTW timer registers
__constant U32 CMTW0_BASE       = 0x80001000;
__constant U32 CMTW0_CMWCOR     = 0x80001004;

// Flash commands
__constant U32 FLASH_CMD_RDID   = 0x9F00;      // Read Identification
__constant U32 FLASH_CMD_RSTEN  = 0x6600;      // Reset Enable
__constant U32 FLASH_CMD_RST    = 0x9900;      // Reset
__constant U32 FLASH_CMD_WREN   = 0x0600;      // Write Enable
__constant U32 FLASH_CMD_READ4B = 0x0300;      // Normal Read 4-byte
__constant U32 FLASH_CMD_PP4B   = 0x0200;      // Page Program 4-byte
__constant U32 FLASH_CMD_RDSR   = 0x0500;      // Read Status Register
__constant U32 FLASH_CMD_SE4B   = 0x2000;      // Sector Erase 4KB 4-byte

// Flash parameters
__constant U32 FLASH_ID         = 0x0001840C8; // MX25UR51245G Device ID
__constant U32 FLASH_SECTOR_SIZE = 4096;       // 4KB sector size
__constant U32 FLASH_PAGE_SIZE  = 256;         // 256 byte page size
__constant U32 FLASH_SECTOR_COUNT = 16384;     // Total sectors

/*********************************************************************
*
*       Static code
*
**********************************************************************
*/

/*********************************************************************
*
*       _SetClock400MHz()
*
*  Function description
*    Configure system clock to 400MHz
*/
static void _SetClock400MHz(void) {
  Report("Setting system clock to 400MHz...");
  
  // Unlock protection registers
  JLINK_MEM_WriteU32(PRCRS_ADDR, PRCRS_UNLOCK);
  JLINK_MEM_WriteU32(PRCRN_ADDR, PRCRN_UNLOCK);

  // Set SCKCR2 for 400MHz operation
  JLINK_MEM_WriteU32(SCKCR2_ADDR, 0x00000015);

  // Lock protection registers
  JLINK_MEM_WriteU32(PRCRS_ADDR, PRCRS_LOCK);
  JLINK_MEM_WriteU32(PRCRN_ADDR, PRCRN_LOCK);
}

/*********************************************************************
*
*       _SetupXSPIPins()
*
*  Function description
*    Configure XSPI pins for SPI flash interface
*/
static void _SetupXSPIPins(void) {
  Report("Configuring XSPI pins...");
  
  // Unlock protection registers
  JLINK_MEM_WriteU32(PRCRS_ADDR, PRCRS_UNLOCK);
  JLINK_MEM_WriteU32(PRCRN_ADDR, PRCRN_UNLOCK);

  // Enable xSPI module clock
  JLINK_MEM_WriteU32(MSTPCRA_ADDR, 0x00001F01);

  // Configure port pins for XSPI1
  // P17_7 -> XSPI1_CKP, P16_7 -> XSPI1_IO0, P17_0 -> XSPI1_IO1
  // P17_3 -> XSPI1_IO2, P17_4 -> XSPI1_IO3, P18_2 -> XSPI1_CS0#
  JLINK_MEM_WriteU8(ASELP16_ADDR, 0x80);
  JLINK_MEM_WriteU8(ASELP17_ADDR, 0x99);
  JLINK_MEM_WriteU8(ASELP18_ADDR, 0x04);

  JLINK_MEM_WriteU32(PFC16_ADDR, 0x20000000);
  JLINK_MEM_WriteU32(PFC17_ADDR, 0x70077002);
  JLINK_MEM_WriteU32(PFC18_ADDR, 0x00000500);

  JLINK_MEM_WriteU8(PMC16_ADDR, 0x80);
  JLINK_MEM_WriteU8(PMC17_ADDR, 0x99);
  JLINK_MEM_WriteU8(PMC18_ADDR, 0x04);

  // Lock protection registers
  JLINK_MEM_WriteU32(PRCRS_ADDR, PRCRS_LOCK);
  JLINK_MEM_WriteU32(PRCRN_ADDR, PRCRN_LOCK);
}

/*********************************************************************
*
*       _EnableBTCM()
*
*  Function description
*    Enable and clear BTCM (Tightly Coupled Memory)
*/
static void _EnableBTCM(void) {
  U32 Val;
  
  Report("Enabling BTCM...");
  
  // Read current BTCM control register
  Val = JLINK_CORESIGHT_ReadAP(JLINK_CORESIGHT_AP_REG_DATA);
  
  // Configure BTCM: base address, enable at EL2 and EL1/EL0
  Val = BTCM_ADDR | (1 << 1) | (1 << 0);
  
  // Write back to BTCM control register (CP15 register c9, c1, 1)
  JLINK_CORESIGHT_WriteAP(JLINK_CORESIGHT_AP_REG_DATA, Val);
  
  // Clear BTCM memory
  Report("Clearing BTCM memory...");
  JLINK_MEM_Fill(BTCM_ADDR, BTCM_SIZE, 0x00);
}

/*********************************************************************
*
*       _ClearATCM()
*
*  Function description
*    Clear ATCM (Tightly Coupled Memory)
*/
static void _ClearATCM(void) {
  Report("Clearing ATCM memory...");
  JLINK_MEM_Fill(ATCM_ADDR, ATCM_SIZE, 0x00);
}

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/

/*********************************************************************
*
*       InitTarget
*
*  Function description
*    Called before InitTarget() is called.
*    Mainly used to set some global DLL variables to customize the
*    normal connect procedure.
*/
void InitTarget(void) {
  Report("RZ/N2L MX25UR51245G SPI Flash initialization");
  
  // Perform hardware reset
  JLINK_TARGET_Halt();
  
  // Configure system for flash programming
  _SetClock400MHz();
  _SetupXSPIPins();
  _EnableBTCM();
  _ClearATCM();
  
  Report("Target initialization completed");
}

/*********************************************************************
*
*       SetupTarget
*
*  Function description
*    Called after InitTarget() and after general J-Link setup.
*    Can be used to configure the target for flash programming.
*/
void SetupTarget(void) {
  Report("Setting up target for flash programming...");
  
  // Additional setup if needed
  // This function is called before flash programming operations
  
  Report("Target setup completed");
}

/*********************************************************************
*
*       ResetTarget
*
*  Function description
*    Called to reset the target.
*/
void ResetTarget(void) {
  Report("Resetting target...");
  
  // Perform target reset
  JLINK_TARGET_Halt();
  JLINK_TARGET_Reset();
  
  // Re-initialize after reset
  _SetClock400MHz();
  _SetupXSPIPins();
  _EnableBTCM();
  _ClearATCM();
  
  Report("Target reset completed");
}
