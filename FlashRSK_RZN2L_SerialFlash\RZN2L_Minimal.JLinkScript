/*********************************************************************
*                    SEGGER Microcontroller GmbH                     *
*                        The Embedded Experts                        *
**********************************************************************
*                                                                    *
* J-Link Script for Renesas RZ/N2L with MX25UR51245G SPI Flash      *
* Minimal version - only essential initialization                    *
*                                                                    *
**********************************************************************
*/

/*********************************************************************
*
*       InitTarget
*/
void InitTarget(void) {
  Report("RZ/N2L Minimal Initialization");
  
  // Basic system setup only
  JLINK_MEM_WriteU32(0x81281A00, 0x0000A50F);  // Unlock PRCRS
  JLINK_MEM_WriteU32(0x80281A10, 0x0000A50F);  // Unlock PRCRN
  
  JLINK_MEM_WriteU32(0x81280008, 0x00000015);  // Set clock
  JLINK_MEM_WriteU32(0x80280300, 0x00001F01);  // Enable xSPI
  
  JLINK_MEM_WriteU32(0x81281A00, 0x0000A500);  // Lock PRCRS
  JLINK_MEM_WriteU32(0x80281A10, 0x0000A500);  // Lock PRCRN
  
  Report("Minimal initialization done");
}

/*********************************************************************
*
*       SetupTarget
*/
void SetupTarget(void) {
  // Empty - no additional setup needed
}

/*********************************************************************
*
*       ResetTarget
*/
void ResetTarget(void) {
  Report("Reset target");
  JLINK_TIF_ActivateTargetReset();
  JLINK_TIF_ReleaseTargetReset();
  InitTarget();
}
