/*********************************************************************
*                    SEGGER Microcontroller GmbH                     *
*                        The Embedded Experts                        *
**********************************************************************
*                                                                    *
* J-Link Script for Renesas RZ/N2L with MX25UR51245G SPI Flash      *
* Simplified version for basic flash programming operations          *
*                                                                    *
**********************************************************************
*/

/*********************************************************************
*
*       Constants
*
**********************************************************************
*/

// System control registers
__constant U32 PRCRS_ADDR       = 0x81281A00;  // Safety Area Protect Register
__constant U32 PRCRN_ADDR       = 0x80281A10;  // Non-Safety Area Protect Register
__constant U32 MSTPCRA_ADDR     = 0x80280300;  // Module Stop Control Register A
__constant U32 SCKCR2_ADDR      = 0x81280008;  // System Clock Control Register 2

// Protection values
__constant U32 PRCRS_UNLOCK     = 0x0000A50F;
__constant U32 PRCRS_LOCK       = 0x0000A500;

// Memory addresses
__constant U32 BTCM_ADDR        = 0x00100000;
__constant U32 BTCM_SIZE        = 0x00020000;
__constant U32 ATCM_ADDR        = 0x00000000;
__constant U32 ATCM_SIZE        = 0x00020000;

/*********************************************************************
*
*       Local functions
*
**********************************************************************
*/

/*********************************************************************
*
*       _UnlockRegisters()
*/
static void _UnlockRegisters(void) {
  MEM_WriteU32(PRCRS_ADDR, PRCRS_UNLOCK);
  MEM_WriteU32(PRCRN_ADDR, PRCRS_UNLOCK);
}

/*********************************************************************
*
*       _LockRegisters()
*/
static void _LockRegisters(void) {
  MEM_WriteU32(PRCRS_ADDR, PRCRS_LOCK);
  MEM_WriteU32(PRCRN_ADDR, PRCRS_LOCK);
}

/*********************************************************************
*
*       _SetupClock()
*/
static void _SetupClock(void) {
  Report("Configuring system clock...");
  
  _UnlockRegisters();
  
  // Set system clock to 400MHz
  MEM_WriteU32(SCKCR2_ADDR, 0x00000015);
  
  _LockRegisters();
}

/*********************************************************************
*
*       _SetupXSPI()
*/
static void _SetupXSPI(void) {
  Report("Configuring XSPI interface...");
  
  _UnlockRegisters();
  
  // Enable xSPI module
  MEM_WriteU32(MSTPCRA_ADDR, 0x00001F01);
  
  // Configure XSPI pins
  // P17_7 -> XSPI1_CKP, P16_7 -> XSPI1_IO0, P17_0 -> XSPI1_IO1
  // P17_3 -> XSPI1_IO2, P17_4 -> XSPI1_IO3, P18_2 -> XSPI1_CS0#
  MEM_WriteU8(0x81030C10, 0x80);  // ASELP16
  MEM_WriteU8(0x81030C11, 0x99);  // ASELP17
  MEM_WriteU8(0x81030C12, 0x04);  // ASELP18
  
  MEM_WriteU32(0x800A0640, 0x20000000);  // PFC16
  MEM_WriteU32(0x800A0644, 0x70077002);  // PFC17
  MEM_WriteU32(0x800A0648, 0x00000500);  // PFC18
  
  MEM_WriteU8(0x800A0410, 0x80);  // PMC16
  MEM_WriteU8(0x800A0411, 0x99);  // PMC17
  MEM_WriteU8(0x800A0412, 0x04);  // PMC18
  
  _LockRegisters();
}

/*********************************************************************
*
*       _InitMemory()
*/
static void _InitMemory(void) {
  Report("Initializing TCM memory...");
  
  // Clear BTCM and ATCM
  MEM_Fill(BTCM_ADDR, BTCM_SIZE, 0x00);
  MEM_Fill(ATCM_ADDR, ATCM_SIZE, 0x00);
}

/*********************************************************************
*
*       Public functions
*
**********************************************************************
*/

/*********************************************************************
*
*       InitTarget
*/
void InitTarget(void) {
  Report("RZ/N2L Target Initialization");
  
  // Basic target setup
  _SetupClock();
  _SetupXSPI();
  _InitMemory();
  
  Report("Target initialization completed");
}

/*********************************************************************
*
*       SetupTarget
*/
void SetupTarget(void) {
  Report("Setting up target for flash operations");
  // Additional setup can be added here if needed
}

/*********************************************************************
*
*       ResetTarget
*/
void ResetTarget(void) {
  Report("Resetting target");
  
  // Perform reset and re-initialize
  JLINK_TARGET_Reset();
  SYS_Sleep(100);  // Wait 100ms after reset
  
  InitTarget();
  
  Report("Target reset completed");
}
