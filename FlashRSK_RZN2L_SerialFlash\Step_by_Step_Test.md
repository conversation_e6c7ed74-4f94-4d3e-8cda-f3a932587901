# RZ/N2L J-Flash 分步测试指南

## 当前问题分析

您遇到的错误仍然是脚本编译问题，可能的原因：
1. J-Link软件版本不支持某些API
2. 脚本文件路径或编码问题
3. 缓存的旧脚本文件

## 分步测试方案

### 步骤1：基础连接测试（无脚本）

#### 1.1 使用无脚本配置
```
文件：RZN2L_NoScript.jflash
特点：完全不使用初始化脚本
```

**操作步骤：**
1. 打开J-Flash
2. File → Open Project
3. 选择 `RZN2L_NoScript.jflash`
4. Target → Connect

**预期结果：**
- 如果成功：说明硬件连接正常，问题在脚本
- 如果失败：说明硬件连接有问题

#### 1.2 手动初始化测试
如果无脚本连接成功，手动执行初始化：

1. Target → Manual Programming → Memory
2. 在Memory窗口中手动写入寄存器：

```
地址: 0x81281A00, 值: 0x0000A50F  (解锁PRCRS)
地址: 0x80281A10, 值: 0x0000A50F  (解锁PRCRN)
地址: 0x81280008, 值: 0x00000015  (设置时钟)
地址: 0x80280300, 值: 0x00001F01  (使能xSPI)
```

### 步骤2：最简脚本测试

#### 2.1 使用最简脚本
```
文件：RZN2L_Minimal.JLinkScript
特点：只包含最基本的初始化
```

**操作步骤：**
1. 确保使用 `RZN2L_MX25UR51245G.jflash`（已更新为使用Minimal脚本）
2. Target → Connect

#### 2.2 如果仍然失败
创建一个空脚本进行测试：

**创建文件：`Empty.JLinkScript`**
```c
void InitTarget(void) {
  Report("Empty script test");
}

void SetupTarget(void) {
}

void ResetTarget(void) {
}
```

### 步骤3：J-Link Commander测试

#### 3.1 基础连接测试
```batch
运行：test_connection.bat
```

#### 3.2 手动Commander测试
```
1. 打开命令行
2. 运行：JLink.exe
3. 输入命令：
   connect
   device unspecified
   si swd
   speed 1000
   halt
   mem32 0x81281A00, 1
```

### 步骤4：问题诊断

#### 4.1 检查J-Link版本
```
JLink.exe -CommanderScript
输入：?
查看版本信息
```

#### 4.2 检查脚本文件
确认脚本文件：
- 编码为UTF-8或ASCII
- 路径中无中文字符
- 文件完整无损坏

#### 4.3 检查J-Flash设置
在Project Settings中：
- Interface: SWD
- Speed: 1000 kHz（降低速度）
- Target Voltage: 3300 mV
- Reset Type: 尝试不同选项

### 步骤5：替代方案

#### 5.1 使用J-Link RTT Viewer
如果J-Flash无法工作，尝试：
1. J-Link RTT Viewer
2. J-Link GDB Server
3. 原始IAR环境

#### 5.2 使用OpenOCD
如果支持RZ/N2L，可以尝试OpenOCD

## 具体测试命令

### 测试1：无脚本连接
```batch
# 使用RZN2L_NoScript.jflash
JFlash.exe -openprj RZN2L_NoScript.jflash -connect -exit
```

### 测试2：最简脚本连接
```batch
# 使用RZN2L_MX25UR51245G.jflash（Minimal脚本）
JFlash.exe -openprj RZN2L_MX25UR51245G.jflash -connect -exit
```

### 测试3：Commander连接
```batch
# 运行test_connection.bat
test_connection.bat
```

## 错误代码分析

### 脚本编译错误
```
Error while compiling. Line XXX, column X:
```
**解决方案：**
1. 使用RZN2L_Minimal.JLinkScript
2. 或使用RZN2L_NoScript.jflash（无脚本）

### 连接超时
```
Failed to connect. Timeout
```
**解决方案：**
1. 检查硬件连接
2. 降低SWD速度
3. 检查目标板电源

### 目标无响应
```
Could not halt target
```
**解决方案：**
1. 检查复位信号
2. 尝试不同复位类型
3. 检查目标板状态

## 成功标志

### 连接成功
```
Connected successfully
Target halted
```

### 内存访问成功
```
能够读写目标内存
寄存器值正确返回
```

### Flash操作成功
```
能够识别Flash设备
擦除/编程操作正常
```

## 下一步行动

根据测试结果：

1. **如果无脚本连接成功**：
   - 问题在脚本，使用手动初始化
   - 或继续调试脚本问题

2. **如果Commander连接成功但J-Flash失败**：
   - J-Flash配置问题
   - 尝试不同的J-Flash版本

3. **如果所有方法都失败**：
   - 硬件连接问题
   - 目标板状态问题
   - 回到原始IAR环境

---

**建议测试顺序：**
1. test_connection.bat
2. RZN2L_NoScript.jflash
3. RZN2L_MX25UR51245G.jflash（Minimal脚本）
4. 手动内存操作测试
