@echo off
REM ********************************************************************
REM Batch script for programming RZ/N2L SPI Flash using J-Flash
REM ********************************************************************

setlocal enabledelayedexpansion

REM Configuration
set JFLASH_EXE="C:\Program Files\SEGGER\JLink\JFlash.exe"
set PROJECT_FILE=RZN2L_MX25UR51245G.jflash
set SCRIPT_FILE=RZN2L_Minimal.JLinkScript

echo ============================================================
echo RZ/N2L SPI Flash Programming Script
echo ============================================================
echo.

REM Check if J-Flash executable exists
if not exist %JFLASH_EXE% (
    echo ERROR: J-Flash executable not found at %JFLASH_EXE%
    echo Please install J-Link Software or update the path in this script.
    pause
    exit /b 1
)

REM Check if project file exists
if not exist "%PROJECT_FILE%" (
    echo ERROR: J-Flash project file not found: %PROJECT_FILE%
    echo Please ensure the project file is in the current directory.
    pause
    exit /b 1
)

REM Check if script file exists
if not exist "%SCRIPT_FILE%" (
    echo ERROR: J-Link script file not found: %SCRIPT_FILE%
    echo Please ensure the script file is in the current directory.
    pause
    exit /b 1
)

REM Display menu
:MENU
echo.
echo Select operation:
echo 1. Test connection (no script)
echo 2. Test J-Link Script APIs
echo 3. Connect to target with script
echo 4. Erase entire flash
echo 5. Program flash (specify file)
echo 6. Verify flash (specify file)
echo 7. Program and verify (specify file)
echo 8. Open J-Flash GUI with project
echo 9. Exit
echo.
set /p choice="Enter your choice (1-9): "

if "%choice%"=="1" goto TEST_NO_SCRIPT
if "%choice%"=="2" goto TEST_API
if "%choice%"=="3" goto CONNECT
if "%choice%"=="4" goto ERASE
if "%choice%"=="5" goto PROGRAM
if "%choice%"=="6" goto VERIFY
if "%choice%"=="7" goto PROGRAM_VERIFY
if "%choice%"=="8" goto GUI
if "%choice%"=="9" goto EXIT
echo Invalid choice. Please try again.
goto MENU

:TEST_NO_SCRIPT
echo.
echo Testing connection without script...
%JFLASH_EXE% -openprj RZN2L_NoScript.jflash -connect -exit
if %errorlevel%==0 (
    echo Connection test PASSED - Hardware is OK
    echo The problem is likely in the initialization script
) else (
    echo Connection test FAILED - Check hardware connection
)
goto MENU

:TEST_API
echo.
echo Testing J-Link Script APIs...
echo Creating temporary project file for API test...
copy RZN2L_MX25UR51245G.jflash RZN2L_APITest_temp.jflash > nul
powershell -Command "(Get-Content RZN2L_APITest_temp.jflash) -replace 'RZN2L_SafeAPI.JLinkScript', 'RZN2L_APITest.JLinkScript' | Set-Content RZN2L_APITest_temp.jflash"
%JFLASH_EXE% -openprj RZN2L_APITest_temp.jflash -connect -exit
if %errorlevel%==0 (
    echo API test PASSED - Script APIs are working
) else (
    echo API test FAILED - Check J-Link script compatibility
)
if exist RZN2L_APITest_temp.jflash del RZN2L_APITest_temp.jflash
goto MENU

:CONNECT
echo.
echo Connecting to target...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -exit
goto MENU

:ERASE
echo.
echo Erasing flash...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -erase -exit
if %errorlevel%==0 (
    echo Flash erased successfully.
) else (
    echo Flash erase failed.
)
goto MENU

:PROGRAM
echo.
set /p filename="Enter path to binary/hex file: "
if not exist "%filename%" (
    echo ERROR: File not found: %filename%
    goto MENU
)
echo Programming flash with %filename%...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -open%filename% -program -exit
if %errorlevel%==0 (
    echo Programming completed successfully.
) else (
    echo Programming failed.
)
goto MENU

:VERIFY
echo.
set /p filename="Enter path to binary/hex file: "
if not exist "%filename%" (
    echo ERROR: File not found: %filename%
    goto MENU
)
echo Verifying flash with %filename%...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -open%filename% -verify -exit
if %errorlevel%==0 (
    echo Verification completed successfully.
) else (
    echo Verification failed.
)
goto MENU

:PROGRAM_VERIFY
echo.
set /p filename="Enter path to binary/hex file: "
if not exist "%filename%" (
    echo ERROR: File not found: %filename%
    goto MENU
)
echo Programming and verifying flash with %filename%...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -open%filename% -program -verify -exit
if %errorlevel%==0 (
    echo Programming and verification completed successfully.
) else (
    echo Programming or verification failed.
)
goto MENU

:GUI
echo.
echo Opening J-Flash GUI with project...
start "" %JFLASH_EXE% -openprj%PROJECT_FILE%
goto MENU

:EXIT
echo.
echo Exiting...
exit /b 0

REM Error handling
:ERROR
echo.
echo An error occurred during the operation.
echo Please check the J-Flash log for details.
pause
goto MENU
