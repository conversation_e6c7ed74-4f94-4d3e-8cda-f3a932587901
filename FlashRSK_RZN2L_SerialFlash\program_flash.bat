@echo off
REM ********************************************************************
REM Batch script for programming RZ/N2L SPI Flash using J-Flash
REM ********************************************************************

setlocal enabledelayedexpansion

REM Configuration
set JFLASH_EXE="C:\Program Files\SEGGER\JLink\JFlash.exe"
set PROJECT_FILE=RZN2L_MX25UR51245G.jflash
set SCRIPT_FILE=RZN2L_MX25UR51245G_JLink.JLinkScript

echo ============================================================
echo RZ/N2L SPI Flash Programming Script
echo ============================================================
echo.

REM Check if J-Flash executable exists
if not exist %JFLASH_EXE% (
    echo ERROR: J-Flash executable not found at %JFLASH_EXE%
    echo Please install J-Link Software or update the path in this script.
    pause
    exit /b 1
)

REM Check if project file exists
if not exist "%PROJECT_FILE%" (
    echo ERROR: J-Flash project file not found: %PROJECT_FILE%
    echo Please ensure the project file is in the current directory.
    pause
    exit /b 1
)

REM Check if script file exists
if not exist "%SCRIPT_FILE%" (
    echo ERROR: J-Link script file not found: %SCRIPT_FILE%
    echo Please ensure the script file is in the current directory.
    pause
    exit /b 1
)

REM Display menu
:MENU
echo.
echo Select operation:
echo 1. Connect to target only
echo 2. Erase entire flash
echo 3. Program flash (specify file)
echo 4. Verify flash (specify file)
echo 5. Program and verify (specify file)
echo 6. Open J-Flash GUI with project
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto CONNECT
if "%choice%"=="2" goto ERASE
if "%choice%"=="3" goto PROGRAM
if "%choice%"=="4" goto VERIFY
if "%choice%"=="5" goto PROGRAM_VERIFY
if "%choice%"=="6" goto GUI
if "%choice%"=="7" goto EXIT
echo Invalid choice. Please try again.
goto MENU

:CONNECT
echo.
echo Connecting to target...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -exit
goto MENU

:ERASE
echo.
echo Erasing flash...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -erase -exit
if %errorlevel%==0 (
    echo Flash erased successfully.
) else (
    echo Flash erase failed.
)
goto MENU

:PROGRAM
echo.
set /p filename="Enter path to binary/hex file: "
if not exist "%filename%" (
    echo ERROR: File not found: %filename%
    goto MENU
)
echo Programming flash with %filename%...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -open%filename% -program -exit
if %errorlevel%==0 (
    echo Programming completed successfully.
) else (
    echo Programming failed.
)
goto MENU

:VERIFY
echo.
set /p filename="Enter path to binary/hex file: "
if not exist "%filename%" (
    echo ERROR: File not found: %filename%
    goto MENU
)
echo Verifying flash with %filename%...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -open%filename% -verify -exit
if %errorlevel%==0 (
    echo Verification completed successfully.
) else (
    echo Verification failed.
)
goto MENU

:PROGRAM_VERIFY
echo.
set /p filename="Enter path to binary/hex file: "
if not exist "%filename%" (
    echo ERROR: File not found: %filename%
    goto MENU
)
echo Programming and verifying flash with %filename%...
%JFLASH_EXE% -openprj%PROJECT_FILE% -connect -open%filename% -program -verify -exit
if %errorlevel%==0 (
    echo Programming and verification completed successfully.
) else (
    echo Programming or verification failed.
)
goto MENU

:GUI
echo.
echo Opening J-Flash GUI with project...
start "" %JFLASH_EXE% -openprj%PROJECT_FILE%
goto MENU

:EXIT
echo.
echo Exiting...
exit /b 0

REM Error handling
:ERROR
echo.
echo An error occurred during the operation.
echo Please check the J-Flash log for details.
pause
goto MENU
