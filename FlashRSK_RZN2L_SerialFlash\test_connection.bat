@echo off
REM ********************************************************************
REM Quick connection test script for RZ/N2L using J-Link Commander
REM ********************************************************************

setlocal enabledelayedexpansion

echo ============================================================
echo RZ/N2L Connection Test Script
echo ============================================================
echo.

REM Check if J-Link Commander exists
set JLINK_EXE="C:\Program Files\SEGGER\JLink\JLink.exe"
if not exist %JLINK_EXE% (
    echo ERROR: J-Link Commander not found at %JLINK_EXE%
    echo Please install J-Link Software or update the path.
    pause
    exit /b 1
)

echo Creating test script...

REM Create temporary J-Link command script
echo connect > jlink_test.jlink
echo device unspecified >> jlink_test.jlink
echo si swd >> jlink_test.jlink
echo speed 4000 >> jlink_test.jlink
echo halt >> jlink_test.jlink
echo echo "Testing basic memory access..." >> jlink_test.jlink
echo mem32 0x81281A00, 1 >> jlink_test.jlink
echo echo "Testing XSPI module registers..." >> jlink_test.jlink
echo mem32 0x80280300, 1 >> jlink_test.jlink
echo echo "Connection test completed" >> jlink_test.jlink
echo exit >> jlink_test.jlink

echo.
echo Running connection test...
echo.

REM Run J-Link Commander with test script
%JLINK_EXE% -CommanderScript jlink_test.jlink

REM Check result
if %errorlevel%==0 (
    echo.
    echo ============================================================
    echo Connection test PASSED
    echo You can now try using J-Flash for flash programming.
    echo ============================================================
) else (
    echo.
    echo ============================================================
    echo Connection test FAILED
    echo Please check:
    echo 1. J-Link hardware connection
    echo 2. Target board power supply
    echo 3. SWD interface wiring
    echo 4. Target board reset state
    echo ============================================================
)

REM Clean up
if exist jlink_test.jlink del jlink_test.jlink

echo.
pause
